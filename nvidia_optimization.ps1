# NVIDIA RTX 3060 OPTIMALIZACE PRO MAXIMÁLNÍ VÝKON
# Spustit jako Administrá<PERSON>!

Write-Host "🎮 NVIDIA RTX 3060 OPTIMALIZACE..." -ForegroundColor Green

# 1. NASTAVENÍ NVIDIA CONTROL PANEL VIA REGISTRY
Write-Host "⚙️ Optimalizuji NVIDIA nastavení..." -ForegroundColor Yellow

# Cesta k NVIDIA registrům
$nvidiaPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000"

# Kontrola existence cesty
if (Test-Path $nvidiaPath) {
    # Maximální výkon
    Set-ItemProperty -Path $nvidiaPath -Name "PowerMizerEnable" -Value 0 -Force
    Set-ItemProperty -Path $nvidiaPath -Name "PowerMizerLevel" -Value 0 -Force
    Set-ItemProperty -Path $nvidiaPath -Name "PowerMizerLevelAC" -Value 0 -Force
    
    # Vypnutí V-Sync
    Set-ItemProperty -Path $nvidiaPath -Name "VerticalSyncTearControl" -Value 0 -Force
    
    # Maximální pre-rendered frames
    Set-ItemProperty -Path $nvidiaPath -Name "PreRenderLimit" -Value 1 -Force
    
    # Texture filtering - Performance
    Set-ItemProperty -Path $nvidiaPath -Name "TextureFilteringQuality" -Value 0 -Force
    
    Write-Host "✅ NVIDIA registry optimalizace dokončena" -ForegroundColor Green
} else {
    Write-Host "⚠️ NVIDIA registry cesta nenalezena" -ForegroundColor Red
}

# 2. NVIDIA PROFILE INSPECTOR NASTAVENÍ (pokud je nainstalován)
Write-Host "🔧 Kontroluji NVIDIA Profile Inspector..." -ForegroundColor Yellow

$nvidiaInspectorPath = "C:\Program Files\NVIDIA Corporation\NVIDIA Profile Inspector\nvidiaProfileInspector.exe"
if (Test-Path $nvidiaInspectorPath) {
    Write-Host "✅ NVIDIA Profile Inspector nalezen" -ForegroundColor Green
    # Zde by byly specifické příkazy pro Profile Inspector
} else {
    Write-Host "⚠️ NVIDIA Profile Inspector není nainstalován" -ForegroundColor Yellow
    Write-Host "💡 Doporučuji stáhnout z: https://github.com/Orbmu2k/nvidiaProfileInspector" -ForegroundColor Cyan
}

# 3. NVIDIA DRIVER OPTIMALIZACE
Write-Host "🚀 Optimalizuji NVIDIA driver..." -ForegroundColor Yellow

# Vypnutí NVIDIA telemetrie
$nvidiaTelemetryServices = @(
    "NvTelemetryContainer",
    "NVDisplay.ContainerLocalSystem"
)

foreach ($service in $nvidiaTelemetryServices) {
    try {
        Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
        Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
        Write-Host "✅ Vypnuto: $service" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Služba nenalezena: $service" -ForegroundColor Yellow
    }
}

# 4. GPU SCHEDULING
Write-Host "⚡ Aktivuji Hardware-accelerated GPU scheduling..." -ForegroundColor Yellow
Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" -Name "HwSchMode" -Value 2 -Force

Write-Host "✅ NVIDIA OPTIMALIZACE DOKONČENA!" -ForegroundColor Green
Write-Host "🔄 RESTARTUJTE POČÍTAČ PRO APLIKOVÁNÍ ZMĚN!" -ForegroundColor Red

# DOPORUČENÉ MANUÁLNÍ NASTAVENÍ V NVIDIA CONTROL PANEL:
Write-Host "`n📋 MANUÁLNÍ NASTAVENÍ V NVIDIA CONTROL PANEL:" -ForegroundColor Cyan
Write-Host "1. Manage 3D Settings > Global Settings:" -ForegroundColor White
Write-Host "   - Power management mode: Prefer maximum performance" -ForegroundColor Gray
Write-Host "   - Texture filtering - Quality: Performance" -ForegroundColor Gray
Write-Host "   - Vertical sync: Off" -ForegroundColor Gray
Write-Host "   - Low Latency Mode: Ultra" -ForegroundColor Gray
Write-Host "   - Max Frame Rate: Off" -ForegroundColor Gray
Write-Host "   - Multi-Frame Sampled AA: Off" -ForegroundColor Gray
Write-Host "   - Shader Cache: On" -ForegroundColor Gray
Write-Host "`n2. Change resolution > Refresh rate: Nejvyšší dostupný" -ForegroundColor White
Write-Host "`n3. Adjust desktop color settings > Use NVIDIA settings" -ForegroundColor White
