# OPTIMALIZACE PRO HERNI VÝVOJ A "LIGHT OR DEAD"
# Spustit jako Administrátor!

Write-Host "🎮 OPTIMALIZACE PRO HERNÍ VÝVOJ..." -ForegroundColor Green

# 1. NASTAVENÍ PRIORITY PROCESŮ PRO DEVELOPMENT
Write-Host "⚡ Nastavuji priority pro development..." -ForegroundColor Yellow

# Funkce pro nastavení priority procesu
function Set-ProcessPriority {
    param(
        [string]$ProcessName,
        [string]$Priority
    )
    
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        foreach ($process in $processes) {
            $process.PriorityClass = $Priority
            Write-Host "✅ Nastavena priorita $Priority pro $ProcessName" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️ Proces $ProcessName nenalezen" -ForegroundColor Yellow
    }
}

# 2. OPTIMALIZACE PRO JAVASCRIPT/NODE.JS DEVELOPMENT
Write-Host "📝 Optimalizuji pro JavaScript development..." -ForegroundColor Yellow

# Zvýšení limitu pro Node.js
$env:NODE_OPTIONS = "--max-old-space-size=8192"
[Environment]::SetEnvironmentVariable("NODE_OPTIONS", "--max-old-space-size=8192", "User")

# 3. OPTIMALIZACE DISKU PRO RYCHLEJŠÍ LOADING
Write-Host "💾 Optimalizuji disk pro rychlejší loading..." -ForegroundColor Yellow

# Vypnutí indexování pro game folder (pokud existuje)
$gamePath = "C:\Users\<USER>\Desktop\lightr or dead"
if (Test-Path $gamePath) {
    # Vypnutí indexování pro rychlejší přístup k souborům
    $folder = Get-Item $gamePath
    $folder.Attributes = $folder.Attributes -bor [System.IO.FileAttributes]::NotContentIndexed
    Write-Host "✅ Vypnuto indexování pro: $gamePath" -ForegroundColor Green
}

# 4. OPTIMALIZACE PAMĚTI PRO DEVELOPMENT
Write-Host "🧠 Optimalizuji paměť..." -ForegroundColor Yellow

# Vyčištění paměti
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()
[System.GC]::Collect()

# Nastavení pro lepší garbage collection
$env:DOTNET_gcServer = "1"
$env:DOTNET_gcConcurrent = "1"

# 5. SÍŤOVÁ OPTIMALIZACE PRO MULTIPLAYER (pokud plánujete)
Write-Host "🌐 Optimalizuji síť pro gaming..." -ForegroundColor Yellow

# TCP optimalizace pro gaming
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled
netsh int tcp set global netdma=enabled
netsh int tcp set heuristics disabled
netsh int tcp set global rsc=enabled

# Nastavení DNS pro rychlejší resolving
netsh interface ip set dns "Wi-Fi" static *******
netsh interface ip add dns "Wi-Fi" ******* index=2

# 6. OPTIMALIZACE PRO WEBGL/CANVAS PERFORMANCE
Write-Host "🎨 Optimalizuji pro WebGL/Canvas..." -ForegroundColor Yellow

# Registry optimalizace pro hardware acceleration
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Internet Explorer\Main" -Name "DisableGPUAcceleration" -Value 0 -Force
Set-ItemProperty -Path "HKCU:\Software\Microsoft\Internet Explorer\Main" -Name "UseSWRender" -Value 0 -Force

# 7. DEVELOPMENT TOOLS OPTIMALIZACE
Write-Host "🛠️ Optimalizuji development tools..." -ForegroundColor Yellow

# VS Code optimalizace
$vsCodeSettings = @{
    "files.watcherExclude" = @{
        "**/node_modules/**" = $true
        "**/.git/objects/**" = $true
        "**/.git/subtree-cache/**" = $true
        "**/dist/**" = $true
        "**/build/**" = $true
    }
    "search.exclude" = @{
        "**/node_modules" = $true
        "**/bower_components" = $true
        "**/dist" = $true
        "**/build" = $true
    }
    "files.exclude" = @{
        "**/node_modules" = $true
        "**/.git" = $true
        "**/.DS_Store" = $true
        "**/Thumbs.db" = $true
    }
}

# 8. REAL-TIME PRIORITY PRO KRITICKÉ PROCESY
Write-Host "🚀 Nastavuji real-time priority..." -ForegroundColor Yellow

# Funkce pro monitoring a automatické nastavení priority
$monitorScript = @"
while (`$true) {
    # Nastavení priority pro browser (pro testování hry)
    Set-ProcessPriority "chrome" "High"
    Set-ProcessPriority "firefox" "High"
    Set-ProcessPriority "msedge" "High"
    
    # Nastavení priority pro development tools
    Set-ProcessPriority "Code" "High"
    Set-ProcessPriority "node" "High"
    Set-ProcessPriority "npm" "High"
    
    Start-Sleep 30
}
"@

# Uložení monitoring scriptu
$monitorScript | Out-File -FilePath "priority_monitor.ps1" -Encoding UTF8

Write-Host "✅ HERNÍ OPTIMALIZACE DOKONČENA!" -ForegroundColor Green
Write-Host "📁 Vytvořen monitoring script: priority_monitor.ps1" -ForegroundColor Cyan
Write-Host "🔄 RESTARTUJTE POČÍTAČ PRO PLNÝ EFEKT!" -ForegroundColor Red

# DOPORUČENÍ PRO LIGHT OR DEAD DEVELOPMENT
Write-Host "`n🎯 DOPORUČENÍ PRO 'LIGHT OR DEAD' DEVELOPMENT:" -ForegroundColor Cyan
Write-Host "1. Použijte Chrome s --disable-web-security pro testování" -ForegroundColor White
Write-Host "2. Nastavte FPS limit v hře na 144 FPS (pro smooth gameplay)" -ForegroundColor White
Write-Host "3. Používejte requestAnimationFrame() místo setInterval()" -ForegroundColor White
Write-Host "4. Implementujte object pooling pro projektily a nepřátele" -ForegroundColor White
Write-Host "5. Používejte Web Workers pro AI výpočty" -ForegroundColor White
