#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Light or Dead - Advanced Adaptive Sound System
==============================================

Pokročilý systém pro vytváření adaptivní hudby a zvuků,
který reaguje na stav hry v reálném čase.

Autor: AI Assistant
Verze: 2.0
"""

import numpy as np
import scipy.signal as signal
import scipy.io.wavfile as wavfile
import os
import random
import math
import json
from typing import Dict, List, Tuple, Optional
import threading
import time

class AdaptiveMusicEngine:
    """Engine pro adaptivní hudbu, která se mění podle stavu hry"""
    
    def __init__(self, sample_rate: int = 44100):
        self.sample_rate = sample_rate
        self.output_dir = "game_assets/sounds/adaptive"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # <PERSON><PERSON><PERSON>n<PERSON> vrstvy pro různé situace
        self.layers = {
            'ambient_base': None,      # Základní ambientní vrstva
            'tension_low': None,       # Nízké napětí
            'tension_medium': None,    # Střední napětí  
            'tension_high': None,      # Vysoké napětí
            'action_drums': None,      # Akční bicí
            'boss_orchestral': None,   # Orchestrální boss hudba
            'victory_melody': None,    # Vítězná melodie
            'death_dirge': None        # Smuteční hudba
        }
        
        # Aktuální stav hudby
        self.current_intensity = 0.0  # 0.0 - 1.0
        self.current_layers = []
        
        print("🎼 Adaptive Music Engine inicializován")
    
    def generate_base_layers(self):
        """Generuje základní hudební vrstvy"""
        print("\n🎵 Generuji adaptivní hudební vrstvy...")
        
        duration = 30.0  # 30 sekund pro každou vrstvu (loop)
        
        # Ambient base - základní atmosféra
        self.layers['ambient_base'] = self._create_ambient_layer(duration)
        self._save_layer('ambient_base', self.layers['ambient_base'])
        
        # Tension layers - postupně se zvyšující napětí
        self.layers['tension_low'] = self._create_tension_layer(duration, intensity=0.3)
        self._save_layer('tension_low', self.layers['tension_low'])
        
        self.layers['tension_medium'] = self._create_tension_layer(duration, intensity=0.6)
        self._save_layer('tension_medium', self.layers['tension_medium'])
        
        self.layers['tension_high'] = self._create_tension_layer(duration, intensity=0.9)
        self._save_layer('tension_high', self.layers['tension_high'])
        
        # Action drums - rytmické prvky pro akci
        self.layers['action_drums'] = self._create_drum_layer(duration)
        self._save_layer('action_drums', self.layers['action_drums'])
        
        # Boss orchestral - epická hudba pro boss fightu
        self.layers['boss_orchestral'] = self._create_boss_layer(duration)
        self._save_layer('boss_orchestral', self.layers['boss_orchestral'])
        
        print("✅ Všechny adaptivní vrstvy vygenerovány!")
    
    def _create_ambient_layer(self, duration: float) -> np.ndarray:
        """Vytvoří základní ambientní vrstvu"""
        samples = int(duration * self.sample_rate)
        ambient = np.zeros(samples)
        
        # Hluboké drone tóny
        drone_freqs = [55, 82.4, 110, 164.8]  # A1, E2, A2, E3
        for i, freq in enumerate(drone_freqs):
            amplitude = 0.1 / (i + 1)  # Klesající amplituda
            drone = self._generate_tone(freq, duration, amplitude)
            ambient += drone
        
        # Jemné harmonické variace
        for i in range(8):
            start_time = i * (duration / 8)
            harm_freq = random.choice([220, 246, 277, 329]) * random.uniform(0.8, 1.2)
            harm_duration = duration / 16
            harmonic = self._generate_tone(harm_freq, harm_duration, 0.05)
            
            start_sample = int(start_time * self.sample_rate)
            end_sample = start_sample + len(harmonic)
            if end_sample <= len(ambient):
                ambient[start_sample:end_sample] += harmonic
        
        return self._add_reverb(ambient, 0.4)
    
    def _create_tension_layer(self, duration: float, intensity: float) -> np.ndarray:
        """Vytvoří vrstvu napětí podle intenzity"""
        samples = int(duration * self.sample_rate)
        tension = np.zeros(samples)
        
        # Dissonantní akordy pro napětí
        dissonant_freqs = [
            [220, 233, 277],    # Dissonantní triáda
            [246, 261, 311],    # Další dissonance
            [277, 293, 349],    # Třetí variace
        ]
        
        chord_duration = duration / len(dissonant_freqs)
        for i, chord_freqs in enumerate(dissonant_freqs):
            start_time = i * chord_duration
            start_sample = int(start_time * self.sample_rate)
            
            chord = np.zeros(int(chord_duration * self.sample_rate))
            for freq in chord_freqs:
                tone = self._generate_tone(freq, chord_duration, 0.1 * intensity)
                chord += tone
            
            end_sample = start_sample + len(chord)
            if end_sample <= len(tension):
                tension[start_sample:end_sample] += chord
        
        # Tremolo efekt pro zvýšení napětí
        tremolo_freq = 4 + intensity * 8  # 4-12 Hz podle intenzity
        t = np.linspace(0, duration, samples)
        tremolo = 1 + 0.3 * intensity * np.sin(2 * np.pi * tremolo_freq * t)
        tension *= tremolo
        
        return self._add_reverb(tension, 0.2 + intensity * 0.3)
    
    def _create_drum_layer(self, duration: float) -> np.ndarray:
        """Vytvoří rytmickou vrstvu"""
        samples = int(duration * self.sample_rate)
        drums = np.zeros(samples)
        
        bpm = 120
        beat_duration = 60.0 / bpm
        beat_samples = int(beat_duration * self.sample_rate)
        
        # Kick drum pattern
        kick_pattern = [1, 0, 0, 1, 0, 1, 0, 0]
        kick_freq = 60
        
        # Snare pattern  
        snare_pattern = [0, 0, 1, 0, 0, 0, 1, 0]
        snare_freq = 200
        
        num_beats = int(duration / beat_duration)
        for beat in range(num_beats):
            pattern_idx = beat % len(kick_pattern)
            start_sample = beat * beat_samples
            
            # Kick drum
            if kick_pattern[pattern_idx]:
                kick = self._generate_drum_hit(kick_freq, beat_duration * 0.3, 0.4)
                end_sample = start_sample + len(kick)
                if end_sample <= len(drums):
                    drums[start_sample:end_sample] += kick
            
            # Snare drum
            if snare_pattern[pattern_idx]:
                snare = self._generate_drum_hit(snare_freq, beat_duration * 0.2, 0.3)
                end_sample = start_sample + len(snare)
                if end_sample <= len(drums):
                    drums[start_sample:end_sample] += snare
        
        return drums
    
    def _create_boss_layer(self, duration: float) -> np.ndarray:
        """Vytvoří epickou boss vrstvu"""
        samples = int(duration * self.sample_rate)
        boss = np.zeros(samples)
        
        # Dramatické orchestrální akordy
        epic_progression = [
            [110, 138, 164],    # A minor
            [123, 155, 185],    # B minor
            [98, 123, 147],     # G minor
            [130, 164, 196],    # C minor
        ]
        
        chord_duration = duration / len(epic_progression)
        for i, chord_freqs in enumerate(epic_progression):
            start_time = i * chord_duration
            start_sample = int(start_time * self.sample_rate)
            
            # Vytvoření mohutného akordu
            chord = np.zeros(int(chord_duration * self.sample_rate))
            for j, freq in enumerate(chord_freqs):
                # Různé oktávy pro plnější zvuk
                for octave in [1, 2, 4]:
                    tone_freq = freq * octave
                    amplitude = 0.15 / octave  # Vyšší oktávy tišší
                    tone = self._generate_tone(tone_freq, chord_duration, amplitude)
                    chord += tone
            
            end_sample = start_sample + len(chord)
            if end_sample <= len(boss):
                boss[start_sample:end_sample] += chord
        
        # Dramatické melodické prvky
        melody_freqs = [440, 493, 523, 587, 659, 698, 784]  # A major scale
        melody_times = np.linspace(0, duration, len(melody_freqs) * 2)
        
        for i, (time, freq) in enumerate(zip(melody_times, melody_freqs * 2)):
            start_sample = int(time * self.sample_rate)
            note_duration = 0.5
            melody_note = self._generate_tone(freq * 2, note_duration, 0.2)
            
            end_sample = start_sample + len(melody_note)
            if end_sample <= len(boss):
                boss[start_sample:end_sample] += melody_note
        
        return self._add_reverb(boss, 0.6)
    
    def _generate_tone(self, frequency: float, duration: float, 
                      amplitude: float = 0.5) -> np.ndarray:
        """Generuje tón s ADSR envelope"""
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)
        
        # Základní sinusová vlna s harmonickými
        fundamental = np.sin(2 * np.pi * frequency * t)
        harmonic2 = 0.3 * np.sin(2 * np.pi * frequency * 2 * t)
        harmonic3 = 0.1 * np.sin(2 * np.pi * frequency * 3 * t)
        
        wave = amplitude * (fundamental + harmonic2 + harmonic3)
        
        # ADSR envelope
        attack_time = min(0.1, duration * 0.1)
        decay_time = min(0.1, duration * 0.1)
        sustain_level = 0.7
        release_time = min(0.2, duration * 0.3)
        
        envelope = self._create_adsr_envelope(samples, attack_time, decay_time, 
                                            sustain_level, release_time)
        
        return wave * envelope
    
    def _generate_drum_hit(self, frequency: float, duration: float, 
                          amplitude: float = 0.5) -> np.ndarray:
        """Generuje úder bicích"""
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)
        
        # Kombinace tónu a šumu pro realistický zvuk bicích
        tone = np.sin(2 * np.pi * frequency * t)
        noise = np.random.normal(0, 0.3, samples)
        
        # Exponenciální útlum
        decay = np.exp(-t * 8)
        
        drum = amplitude * (0.7 * tone + 0.3 * noise) * decay
        
        return drum
    
    def _create_adsr_envelope(self, samples: int, attack_time: float, 
                             decay_time: float, sustain_level: float, 
                             release_time: float) -> np.ndarray:
        """Vytvoří ADSR envelope"""
        envelope = np.ones(samples)
        
        attack_samples = int(attack_time * self.sample_rate)
        decay_samples = int(decay_time * self.sample_rate)
        release_samples = int(release_time * self.sample_rate)
        sustain_samples = samples - attack_samples - decay_samples - release_samples
        
        if attack_samples > 0:
            envelope[:attack_samples] = np.linspace(0, 1, attack_samples)
        
        if decay_samples > 0:
            start_idx = attack_samples
            end_idx = start_idx + decay_samples
            envelope[start_idx:end_idx] = np.linspace(1, sustain_level, decay_samples)
        
        if sustain_samples > 0:
            start_idx = attack_samples + decay_samples
            end_idx = start_idx + sustain_samples
            envelope[start_idx:end_idx] = sustain_level
        
        if release_samples > 0:
            envelope[-release_samples:] = np.linspace(sustain_level, 0, release_samples)
        
        return envelope
    
    def _add_reverb(self, audio: np.ndarray, room_size: float = 0.3) -> np.ndarray:
        """Přidá reverb efekt"""
        delay_samples = int(0.05 * self.sample_rate)
        reverb = np.zeros(len(audio) + delay_samples)
        reverb[:len(audio)] = audio
        
        for i in range(1, 6):
            delay = int(i * 0.03 * self.sample_rate)
            amplitude = (0.6 ** i) * room_size
            if delay < len(reverb) - len(audio):
                reverb[delay:delay + len(audio)] += audio * amplitude
        
        return reverb[:len(audio)]
    
    def _save_layer(self, layer_name: str, audio_data: np.ndarray) -> None:
        """Uloží hudební vrstvu"""
        # Normalizace
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            audio_data = audio_data / max_val
        
        # Stereo
        stereo_data = np.column_stack((audio_data, audio_data))
        
        # Konverze na 16-bit
        audio_int = (stereo_data * 32767).astype(np.int16)
        
        filepath = os.path.join(self.output_dir, f"{layer_name}.wav")
        wavfile.write(filepath, self.sample_rate, audio_int)
        print(f"🎵 Vrstva uložena: {layer_name}.wav")

def create_adaptive_music_config():
    """Vytvoří konfigurační soubor pro adaptivní hudbu"""
    config = {
        "adaptive_music": {
            "layers": {
                "ambient_base": {
                    "file": "adaptive/ambient_base.wav",
                    "volume": 0.6,
                    "loop": True,
                    "conditions": ["always"]
                },
                "tension_low": {
                    "file": "adaptive/tension_low.wav", 
                    "volume": 0.4,
                    "loop": True,
                    "conditions": ["enemies_nearby", "low_health"]
                },
                "tension_medium": {
                    "file": "adaptive/tension_medium.wav",
                    "volume": 0.5,
                    "loop": True,
                    "conditions": ["many_enemies", "medium_health"]
                },
                "tension_high": {
                    "file": "adaptive/tension_high.wav",
                    "volume": 0.6,
                    "loop": True,
                    "conditions": ["surrounded", "very_low_health"]
                },
                "action_drums": {
                    "file": "adaptive/action_drums.wav",
                    "volume": 0.7,
                    "loop": True,
                    "conditions": ["combat_active", "shooting"]
                },
                "boss_orchestral": {
                    "file": "adaptive/boss_orchestral.wav",
                    "volume": 0.8,
                    "loop": True,
                    "conditions": ["boss_fight"]
                }
            },
            "transitions": {
                "fade_time": 2.0,
                "crossfade": True
            }
        }
    }
    
    config_path = "game_assets/sounds/adaptive_music_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Konfigurace uložena: {config_path}")

def main():
    """Hlavní funkce pro generování adaptivní hudby"""
    print("🎼 Light or Dead - Advanced Adaptive Sound System")
    print("=" * 60)
    
    # Vytvoření adaptivního music engine
    music_engine = AdaptiveMusicEngine()
    
    # Generování všech vrstev
    music_engine.generate_base_layers()
    
    # Vytvoření konfigurace
    create_adaptive_music_config()
    
    print("\n✅ ADAPTIVNÍ HUDEBNÍ SYSTÉM DOKONČEN!")
    print("📁 Soubory uloženy do: game_assets/sounds/adaptive/")
    print("📄 Konfigurace: game_assets/sounds/adaptive_music_config.json")
    print("\n🎮 Nyní můžete implementovat adaptivní hudbu do hry!")
    print("\n💡 Tip: Hudební vrstvy se automaticky přepínají podle stavu hry")

if __name__ == "__main__":
    main()
