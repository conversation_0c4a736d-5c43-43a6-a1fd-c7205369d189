# BRUTÁLNÍ OPTIMALIZACE VÝKONU - WINDOWS 11
# Spustit jako Administrátor!

Write-Host "🚀 ZAČÍNÁM BRUTÁLNÍ OPTIMALIZACI VÝKONU..." -ForegroundColor Green

# 1. NASTAVENÍ VÝKONOVÉHO REŽIMU
Write-Host "⚡ Nastavuji vysoký výkon..." -ForegroundColor Yellow
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
powercfg /setacvalueindex 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 0

# 2. VYPNUTÍ NEPOTŘEBNÝCH SLUŽEB
Write-Host "🛑 Vypínám nepotřebné služby..." -ForegroundColor Yellow
$servicesToDisable = @(
    "Fax",
    "WSearch",
    "<PERSON>pooler",
    "<PERSON>ys<PERSON>ain",
    "Themes",
    "TabletInputService",
    "WbioSrvc",
    "WMPNetworkSvc",
    "XblAuthManager",
    "XblGameSave",
    "XboxGipSvc",
    "XboxNetApiSvc"
)

foreach ($service in $servicesToDisable) {
    try {
        Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
        Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
        Write-Host "✅ Vypnuto: $service" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Nelze vypnout: $service" -ForegroundColor Red
    }
}

# 3. OPTIMALIZACE REGISTRŮ PRO VÝKON
Write-Host "📝 Optimalizuji registry..." -ForegroundColor Yellow

# Vypnutí Windows Defender (POZOR: Snižuje bezpečnost!)
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender" -Name "DisableAntiSpyware" -Value 1 -Force

# Vypnutí telemetrie
Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection" -Name "AllowTelemetry" -Value 0 -Force

# Optimalizace pro hry
Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" -Name "SystemResponsiveness" -Value 0 -Force
Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" -Name "GPU Priority" -Value 8 -Force
Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" -Name "Priority" -Value 6 -Force

# Vypnutí automatických aktualizací
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" -Value 1 -Force

# 4. NASTAVENÍ VIRTUÁLNÍ PAMĚTI
Write-Host "💾 Optimalizuji virtuální paměť..." -ForegroundColor Yellow
$cs = Get-WmiObject -Class Win32_ComputerSystem
$cs.AutomaticManagedPagefile = $false
$cs.Put()

$pageFile = Get-WmiObject -Class Win32_PageFileSetting
if ($pageFile) {
    $pageFile.Delete()
}

# Nastavení na 16GB (optimální pro 32GB RAM)
Set-WmiInstance -Class Win32_PageFileSetting -Arguments @{name="C:\pagefile.sys"; InitialSize=16384; MaximumSize=16384}

# 5. VYPNUTÍ VIZUÁLNÍCH EFEKTŮ
Write-Host "🎨 Vypínám vizuální efekty..." -ForegroundColor Yellow
Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2 -Force

# 6. OPTIMALIZACE SÍŤĚ
Write-Host "🌐 Optimalizuji síť..." -ForegroundColor Yellow
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled
netsh int tcp set global netdma=enabled

# 7. GAME MODE
Write-Host "🎮 Aktivuji Game Mode..." -ForegroundColor Yellow
Set-ItemProperty -Path "HKCU:\Software\Microsoft\GameBar" -Name "AutoGameModeEnabled" -Value 1 -Force

Write-Host "✅ OPTIMALIZACE DOKONČENA!" -ForegroundColor Green
Write-Host "🔄 RESTARTUJTE POČÍTAČ PRO APLIKOVÁNÍ VŠECH ZMĚN!" -ForegroundColor Red
